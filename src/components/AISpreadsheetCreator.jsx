import React, { useState } from 'react';

const AISpreadsheetCreator = () => {
  const [topic, setTopic] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState('');
  const [columns, setColumns] = useState([]);
  const [newColumn, setNewColumn] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [previewData, setPreviewData] = useState(null);

  const templates = [
    { id: 'budget', name: 'Budget Tracker', columns: ['Category', 'Amount', 'Date', 'Notes'] },
    { id: 'project', name: 'Project Timeline', columns: ['Task', 'Start Date', 'End Date', 'Status', 'Assignee'] },
    { id: 'sales', name: 'Sales Data', columns: ['Product', 'Quantity', 'Price', 'Total', 'Customer'] },
    { id: 'inventory', name: 'Inventory Management', columns: ['Item', 'SKU', 'Quantity', 'Location', 'Reorder Level'] },
    { id: 'custom', name: 'Custom Template', columns: [] }
  ];

  const handleTemplateSelect = (template) => {
    setSelectedTemplate(template.id);
    setColumns(template.columns);
  };

  const addColumn = () => {
    if (newColumn.trim() && !columns.includes(newColumn.trim())) {
      setColumns([...columns, newColumn.trim()]);
      setNewColumn('');
    }
  };

  const removeColumn = (index) => {
    setColumns(columns.filter((_, i) => i !== index));
  };

  const handleGenerate = async () => {
    if (!topic.trim() || columns.length === 0) return;
    setIsGenerating(true);
    
    // Simulate AI generation
    setTimeout(() => {
      const mockData = Array.from({ length: 5 }, (_, i) => {
        const row = {};
        columns.forEach(col => {
          row[col] = `Sample ${col} ${i + 1}`;
        });
        return row;
      });
      setPreviewData(mockData);
      setIsGenerating(false);
    }, 2000);
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">
          AI Spreadsheet Creator
        </h1>
        <p className="text-gray-600 mb-8">
          Generate intelligent spreadsheets for any topic using AI
        </p>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column - Configuration */}
          <div className="space-y-6">
            {/* Topic Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Topic or Description
              </label>
              <textarea
                value={topic}
                onChange={(e) => setTopic(e.target.value)}
                placeholder="e.g., Monthly budget tracker, Project timeline, Sales data analysis..."
                className="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={3}
              />
            </div>

            {/* Template Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Choose Template
              </label>
              <div className="grid grid-cols-1 gap-2">
                {templates.map((template) => (
                  <button
                    key={template.id}
                    onClick={() => handleTemplateSelect(template)}
                    className={`p-3 text-left border rounded-lg transition-colors ${
                      selectedTemplate === template.id
                        ? 'border-blue-500 bg-blue-50 text-blue-700'
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                  >
                    <div className="font-medium">{template.name}</div>
                    <div className="text-sm text-gray-500">
                      {template.columns.length > 0 ? template.columns.join(', ') : 'Customizable columns'}
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Column Customization */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Customize Columns
              </label>
              <div className="flex gap-2 mb-3">
                <input
                  type="text"
                  value={newColumn}
                  onChange={(e) => setNewColumn(e.target.value)}
                  placeholder="Add column name"
                  className="flex-1 p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  onKeyPress={(e) => e.key === 'Enter' && addColumn()}
                />
                <button
                  onClick={addColumn}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                >
                  Add
                </button>
              </div>
              <div className="flex flex-wrap gap-2">
                {columns.map((column, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                  >
                    {column}
                    <button
                      onClick={() => removeColumn(index)}
                      className="text-blue-600 hover:text-blue-800"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
            </div>

            <button
              onClick={handleGenerate}
              disabled={!topic.trim() || columns.length === 0 || isGenerating}
              className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isGenerating ? 'Generating...' : 'Create Spreadsheet'}
            </button>
          </div>

          {/* Right Column - Preview */}
          <div>
            <h3 className="text-lg font-medium text-gray-800 mb-4">Preview</h3>
            {previewData ? (
              <div className="border border-gray-300 rounded-lg overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        {columns.map((column, index) => (
                          <th
                            key={index}
                            className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            {column}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {previewData.map((row, rowIndex) => (
                        <tr key={rowIndex}>
                          {columns.map((column, colIndex) => (
                            <td
                              key={colIndex}
                              className="px-4 py-3 whitespace-nowrap text-sm text-gray-900"
                            >
                              {row[column]}
                            </td>
                          ))}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                <div className="px-4 py-3 bg-gray-50 border-t border-gray-200">
                  <div className="flex gap-2">
                    <button className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
                      Download CSV
                    </button>
                    <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                      Download Excel
                    </button>
                    <button className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700">
                      Copy to Clipboard
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center text-gray-500">
                <div className="text-4xl mb-4">📊</div>
                <p>Your generated spreadsheet will appear here</p>
                <p className="text-sm mt-2">Configure your topic and columns, then click "Create Spreadsheet"</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AISpreadsheetCreator;
